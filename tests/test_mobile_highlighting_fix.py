#!/usr/bin/env python3
"""
Test Mobile Highlighting Fix
This script tests the fixed mobile highlighting system to ensure it works without interfering with tests.
"""

import sys
import asyncio
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from mobile.tester import MobileTester
from utils.console import get_centralized_console

console = get_centralized_console()

async def test_highlighting_fix():
    """Test the fixed highlighting system"""
    
    console.print("[bold blue]🧪 Testing Mobile Highlighting Fix[/bold blue]")
    console.print("=" * 60)
    
    try:
        # Initialize mobile tester
        tester = MobileTester()
        
        console.print("[cyan]📱 Initializing mobile tester...[/cyan]")
        
        # Test 1: Check highlighting configuration
        console.print("\n[yellow]Test 1: Highlighting Configuration[/yellow]")
        console.print(f"   Visual feedback enabled: {tester.visual_feedback_enabled}")
        console.print(f"   Highlight enabled: {tester.highlight_enabled}")
        console.print(f"   Simple highlighting only: {tester.simple_highlighting_only}")
        console.print(f"   Highlight duration: {tester.highlight_duration}s")
        
        # Test 2: Test disabling highlighting
        console.print("\n[yellow]Test 2: Disable Highlighting[/yellow]")
        tester.disable_visual_highlighting()
        console.print(f"   After disable - Visual feedback: {tester.visual_feedback_enabled}")
        console.print(f"   After disable - Highlight enabled: {tester.highlight_enabled}")
        
        # Test 3: Test enabling simple highlighting
        console.print("\n[yellow]Test 3: Enable Simple Highlighting[/yellow]")
        tester.enable_simple_highlighting()
        console.print(f"   After enable - Visual feedback: {tester.visual_feedback_enabled}")
        console.print(f"   After enable - Highlight enabled: {tester.highlight_enabled}")
        console.print(f"   After enable - Simple only: {tester.simple_highlighting_only}")
        
        # Test 4: Test highlighting methods (safe)
        console.print("\n[yellow]Test 4: Test Highlighting Methods[/yellow]")
        
        # Test with highlighting disabled
        tester.disable_visual_highlighting()
        console.print("   Testing with highlighting disabled:")
        await tester._add_visual_highlight(None, "CLICK", "Test Button")
        
        # Test with simple highlighting enabled
        tester.enable_simple_highlighting()
        console.print("   Testing with simple highlighting enabled:")
        await tester._add_visual_highlight(None, "INPUT", "Test Input Field")
        
        # Test 5: Device connection (optional)
        console.print("\n[yellow]Test 5: Device Connection Check[/yellow]")
        device_serial = await tester._get_device_serial()
        if device_serial:
            console.print(f"   ✅ Device connected: {device_serial}")
            
            # Test show touches (safe method)
            console.print("   Testing show touches (safe method)...")
            try:
                await tester._enable_show_touches(device_serial)
                console.print("   ✅ Show touches test completed")
            except Exception as e:
                console.print(f"   ⚠️ Show touches failed (expected on some devices): {e}")
        else:
            console.print("   ⚠️ No device connected (this is fine for testing)")
        
        console.print("\n[bold green]✅ All highlighting fix tests completed successfully![/bold green]")
        console.print("[cyan]The highlighting system is now safe and won't interfere with tests.[/cyan]")
        
        return True
        
    except Exception as e:
        console.print(f"\n[red]❌ Test failed: {e}[/red]")
        return False

async def test_mobile_test_compatibility():
    """Test compatibility with mobile-test command"""
    
    console.print("\n[bold cyan]🔧 Testing Mobile-Test Compatibility[/bold cyan]")
    console.print("=" * 60)
    
    try:
        # Initialize mobile tester (same as mobile-test would do)
        tester = MobileTester()
        
        # Test initialization
        console.print("[cyan]📱 Testing mobile tester initialization...[/cyan]")
        
        # This simulates what happens when mobile-test runs
        console.print("   Simulating mobile-test initialization...")
        
        # Test highlighting in different scenarios
        test_scenarios = [
            ("CLICK", "Login Button"),
            ("INPUT", "Username Field"),
            ("ASSERT", "Welcome Message"),
            ("SCROLL", "Content Area"),
            ("NAVIGATE", "Settings Page")
        ]
        
        for action_type, element_text in test_scenarios:
            console.print(f"   Testing {action_type} action on '{element_text}'...")
            await tester._add_visual_highlight(None, action_type, element_text)
        
        console.print("\n[green]✅ Mobile-test compatibility test passed![/green]")
        console.print("[cyan]The highlighting system is compatible with mobile-test.[/cyan]")
        
        return True
        
    except Exception as e:
        console.print(f"\n[red]❌ Compatibility test failed: {e}[/red]")
        return False

async def main():
    """Main test function"""
    try:
        # Run highlighting fix tests
        fix_success = await test_highlighting_fix()
        
        # Run compatibility tests
        compat_success = await test_mobile_test_compatibility()
        
        if fix_success and compat_success:
            console.print("\n[bold green]🎉 All tests passed! Mobile highlighting is fixed.[/bold green]")
            console.print("\n[bold cyan]📋 Summary of fixes:[/bold cyan]")
            console.print("   • Simplified highlighting system")
            console.print("   • Removed problematic complex highlighting methods")
            console.print("   • Added option to disable highlighting completely")
            console.print("   • Reduced highlighting duration to minimize interference")
            console.print("   • Added safe fallbacks for all highlighting operations")
            console.print("\n[bold green]🚀 You can now run mobile-test without highlighting issues![/bold green]")
        else:
            console.print("\n[bold red]❌ Some tests failed. Please check the implementation.[/bold red]")
            
    except KeyboardInterrupt:
        console.print("\n[yellow]⚠️ Tests interrupted by user[/yellow]")
    except Exception as e:
        console.print(f"\n[red]❌ Test error: {e}[/red]")

if __name__ == "__main__":
    asyncio.run(main())
