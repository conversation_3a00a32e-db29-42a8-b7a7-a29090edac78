#!/usr/bin/env python3
"""
Debug Element Detection
Test script to debug why element detection is failing
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from utils.android_manager import AndroidManager
from utils.console import get_centralized_console

console = get_centralized_console()

async def debug_element_detection():
    """Debug element detection issues"""
    try:
        console.print("[cyan]🔧 Starting element detection debug...[/cyan]")
        
        # Initialize Android manager
        android_manager = AndroidManager()
        await android_manager.initialize()
        
        # Get devices
        devices = await android_manager.get_connected_devices()
        if not devices:
            console.print("[red]❌ No devices found[/red]")
            return

        console.print(f"[green]✅ Found {len(devices)} device(s)[/green]")

        # Connect to device
        connection_result = await android_manager.connect_device()
        if not connection_result.get("success"):
            console.print(f"[red]❌ Failed to connect to device: {connection_result.get('error')}[/red]")
            return

        device = android_manager.device
        if not device:
            console.print("[red]❌ Device is None after connection[/red]")
            return

        console.print(f"[green]✅ Connected to device: {device}[/green]")

        # Test basic device info
        try:
            info = device.info
            console.print(f"[cyan]📱 Device info: {info}[/cyan]")
        except Exception as e:
            console.print(f"[red]❌ Failed to get device info: {e}[/red]")
            return
        
        # Test current app
        try:
            current_app = device.app_current()
            console.print(f"[cyan]📱 Current app: {current_app}[/cyan]")
        except Exception as e:
            console.print(f"[red]❌ Failed to get current app: {e}[/red]")
        
        # Test hierarchy dump
        try:
            console.print("[cyan]🌳 Testing hierarchy dump...[/cyan]")
            hierarchy = device.dump_hierarchy()
            if hierarchy:
                console.print(f"[green]✅ Hierarchy dump successful: {len(hierarchy)} characters[/green]")
                # Show first 500 characters
                console.print(f"[cyan]Preview: {hierarchy[:500]}...[/cyan]")
            else:
                console.print("[red]❌ Hierarchy dump returned empty[/red]")
        except Exception as e:
            console.print(f"[red]❌ Hierarchy dump failed: {e}[/red]")
        
        # Test XPath selectors
        selectors = [
            "//*",
            "//*[@bounds]",
            "//*[@clickable='true']",
            "//*[@enabled='true']",
            "//*[string-length(@text)>0]",
            "//*[@resource-id]"
        ]
        
        for selector in selectors:
            try:
                console.print(f"[cyan]🔍 Testing selector: {selector}[/cyan]")
                elements = device.xpath(selector).all()
                console.print(f"[green]✅ Found {len(elements)} elements with '{selector}'[/green]")
                
                # Show details of first few elements
                for i, element in enumerate(elements[:3]):
                    try:
                        text = getattr(element, 'text', '') or ''
                        resource_id = getattr(element, 'resource_id', '') or ''
                        class_name = getattr(element, 'class_name', '') or ''
                        bounds = getattr(element, 'bounds', '') or ''
                        clickable = getattr(element, 'clickable', False)
                        
                        console.print(f"[cyan]  Element {i+1}: text='{text[:30]}', id='{resource_id}', class='{class_name}', clickable={clickable}[/cyan]")
                    except Exception as e:
                        console.print(f"[yellow]  Element {i+1}: Failed to get details: {e}[/yellow]")
                        
                if len(elements) > 0:
                    break  # Found elements, no need to test other selectors
                    
            except Exception as e:
                console.print(f"[red]❌ Selector '{selector}' failed: {e}[/red]")
        
        # Test target app
        target_app = "com.kemendikdasmen.rumahpendidikan"
        console.print(f"[cyan]📱 Testing target app: {target_app}[/cyan]")

        try:
            # Check if app is installed
            app_info = device.app_info(target_app)
            console.print(f"[green]✅ Target app is installed: {app_info}[/green]")

            # Try to start the app
            console.print(f"[cyan]🚀 Starting target app...[/cyan]")
            device.app_start(target_app)

            # Wait a moment for app to start
            await asyncio.sleep(3)

            # Check current app again
            current_app = device.app_current()
            console.print(f"[cyan]📱 Current app after start: {current_app}[/cyan]")

            if current_app.get('package') == target_app:
                console.print("[green]✅ Target app is now running[/green]")

                # Test element detection in target app
                console.print("[cyan]🔍 Testing element detection in target app...[/cyan]")
                elements = device.xpath("//*").all()
                console.print(f"[green]✅ Found {len(elements)} elements in target app[/green]")

                # Show some element details
                for i, element in enumerate(elements[:5]):
                    try:
                        text = getattr(element, 'text', '') or ''
                        resource_id = getattr(element, 'resource_id', '') or ''
                        class_name = getattr(element, 'class_name', '') or ''
                        clickable = getattr(element, 'clickable', False)

                        console.print(f"[cyan]  Target App Element {i+1}: text='{text[:30]}', id='{resource_id}', class='{class_name}', clickable={clickable}[/cyan]")
                    except Exception as e:
                        console.print(f"[yellow]  Target App Element {i+1}: Failed to get details: {e}[/yellow]")
            else:
                console.print(f"[yellow]⚠️ Target app not running. Current: {current_app.get('package')}[/yellow]")

        except Exception as e:
            console.print(f"[red]❌ Target app test failed: {e}[/red]")

        # Test the robust element collector
        console.print("[cyan]🔧 Testing RobustElementCollector...[/cyan]")
        try:
            from mobile.robust_element_collector import RobustElementCollector
            from storage.persistent_excel_manager import PersistentExcelManager

            # Initialize components
            collector = RobustElementCollector({})
            storage = PersistentExcelManager("com.kemendikdasmen.rumahpendidikan")

            # Test collection
            result = await collector.collect_all_elements_robustly(
                device,
                "com.kemendikdasmen.rumahpendidikan",
                storage
            )

            if result and result.get('success'):
                total_elements = result.get('total_elements', 0)
                console.print(f"[green]✅ RobustElementCollector found {total_elements} elements[/green]")

                # Show result details
                console.print(f"[cyan]  Result: {result}[/cyan]")
            else:
                console.print(f"[red]❌ RobustElementCollector failed: {result}[/red]")

        except Exception as e:
            console.print(f"[red]❌ RobustElementCollector test failed: {e}[/red]")

        console.print("[green]🎉 Element detection debug completed[/green]")

    except Exception as e:
        console.print(f"[red]❌ Debug failed: {e}[/red]")

if __name__ == "__main__":
    asyncio.run(debug_element_detection())
