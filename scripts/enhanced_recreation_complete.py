# Enhanced Element Recreation - Complete Implementation

# MAIN ENHANCED RECREATION METHOD

async def _recreate_element_from_data_enhanced(self, element_data: Dict) -> Optional[UiObject]:
    """
    ENHANCED element recreation with comprehensive strategies and fallbacks
    
    This method addresses the critical issue where AI analysis fails due to
    element recreation failures, implementing multiple strategies and fallbacks.
    """
    try:
        if not element_data or not self.device:
            console.print("[red]❌ Invalid element_data or device not initialized[/red]")
            return None

        # Extract element attributes with correct field names
        text = element_data.get("text", "").strip()
        content_desc = element_data.get("content_desc", "").strip()
        resource_id = element_data.get("resource_id", "").strip()
        class_name = element_data.get("class_name", "").strip()
        bounds = element_data.get("bounds")
        clickable = element_data.get("clickable", False)

        console.print(f"[cyan]🔍 Enhanced recreation: text='{text[:30]}...', desc='{content_desc[:30]}...', id='{resource_id}'[/cyan]")

        # STRATEGY 1: High-priority exact matches (most reliable)
        high_priority_strategies = []
        
        if resource_id:
            high_priority_strategies.append(("resource_id_exact", lambda: self.device(resourceId=resource_id)))
        
        if text and len(text) > 2:
            high_priority_strategies.append(("text_exact", lambda: self.device(text=text)))
        
        if content_desc and len(content_desc) > 2:
            high_priority_strategies.append(("desc_exact", lambda: self.device(description=content_desc)))

        # Try high-priority strategies first
        for strategy_name, strategy_func in high_priority_strategies:
            try:
                console.print(f"[cyan]  🎯 Trying {strategy_name}...[/cyan]")
                element = strategy_func()
                if element and element.exists():
                    # Verify element is actually clickable if expected
                    if not clickable or element.info.get('clickable', False):
                        console.print(f"[green]✅ Found using {strategy_name}[/green]")
                        return element
                    else:
                        console.print(f"[yellow]⚠️ {strategy_name} found but not clickable[/yellow]")
                else:
                    console.print(f"[yellow]⚠️ {strategy_name} - not found[/yellow]")
            except Exception as e:
                console.print(f"[red]❌ {strategy_name} failed: {e}[/red]")

        # STRATEGY 2: Fuzzy matching strategies
        fuzzy_strategies = []
        
        if text and len(text) > 3:
            fuzzy_strategies.append(("text_contains", lambda: self.device(textContains=text)))
            # Try partial text matching
            if len(text) > 10:
                partial_text = text[:len(text)//2]
                fuzzy_strategies.append(("text_partial", lambda: self.device(textContains=partial_text)))
        
        if content_desc and len(content_desc) > 3:
            fuzzy_strategies.append(("desc_contains", lambda: self.device(descriptionContains=content_desc)))

        # Try fuzzy strategies
        for strategy_name, strategy_func in fuzzy_strategies:
            try:
                console.print(f"[cyan]  🔍 Trying {strategy_name}...[/cyan]")
                element = strategy_func()
                if element and element.exists():
                    console.print(f"[green]✅ Found using {strategy_name}[/green]")
                    return element
                else:
                    console.print(f"[yellow]⚠️ {strategy_name} - not found[/yellow]")
            except Exception as e:
                console.print(f"[red]❌ {strategy_name} failed: {e}[/red]")

        # STRATEGY 3: Combined strategies
        combined_strategies = []
        
        if text and class_name:
            combined_strategies.append(("text_class", lambda: self.device(text=text, className=class_name)))
        
        if text and resource_id:
            combined_strategies.append(("text_id", lambda: self.device(text=text, resourceId=resource_id)))
        
        if content_desc and class_name:
            combined_strategies.append(("desc_class", lambda: self.device(description=content_desc, className=class_name)))

        # Try combined strategies
        for strategy_name, strategy_func in combined_strategies:
            try:
                console.print(f"[cyan]  🔗 Trying {strategy_name}...[/cyan]")
                element = strategy_func()
                if element and element.exists():
                    console.print(f"[green]✅ Found using {strategy_name}[/green]")
                    return element
                else:
                    console.print(f"[yellow]⚠️ {strategy_name} - not found[/yellow]")
            except Exception as e:
                console.print(f"[red]❌ {strategy_name} failed: {e}[/red]")

        # STRATEGY 4: XPath strategies
        xpath_strategies = []
        
        if text:
            xpath_strategies.append(("xpath_text", f"//*[@text='{text}']"))
            xpath_strategies.append(("xpath_text_contains", f"//*[contains(@text, '{text}')]"))
        
        if content_desc:
            xpath_strategies.append(("xpath_desc", f"//*[@content-desc='{content_desc}']"))
            xpath_strategies.append(("xpath_desc_contains", f"//*[contains(@content-desc, '{content_desc}')]"))
        
        if resource_id:
            xpath_strategies.append(("xpath_id", f"//*[@resource-id='{resource_id}']"))

        # Try XPath strategies
        for strategy_name, xpath_expr in xpath_strategies:
            try:
                console.print(f"[cyan]  🗺️ Trying {strategy_name}...[/cyan]")
                xpath_element = self.device.xpath(xpath_expr)
                if xpath_element.exists:
                    element = xpath_element.get(timeout=2)
                    if element:
                        console.print(f"[green]✅ Found using {strategy_name}[/green]")
                        return element
                console.print(f"[yellow]⚠️ {strategy_name} - not found[/yellow]")
            except Exception as e:
                console.print(f"[red]❌ {strategy_name} failed: {e}[/red]")

        # STRATEGY 5: Class-based strategies (less reliable)
        if class_name:
            try:
                console.print(f"[cyan]  📦 Trying class_name...[/cyan]")
                elements = self.device(className=class_name)
                if elements.exists():
                    # If multiple elements, try to find the best match
                    all_elements = elements.all()
                    for elem in all_elements:
                        elem_info = elem.info
                        elem_text = elem_info.get('text', '')
                        elem_desc = elem_info.get('contentDescription', '')
                        
                        # Check if this element matches our criteria better
                        if ((text and text in elem_text) or 
                            (content_desc and content_desc in elem_desc) or
                            (not text and not content_desc)):
                            console.print(f"[green]✅ Found best match using class_name[/green]")
                            return elem
                    
                    # If no perfect match, return first element
                    console.print(f"[yellow]⚠️ Using first class_name match[/yellow]")
                    return elements
                else:
                    console.print(f"[yellow]⚠️ class_name - not found[/yellow]")
            except Exception as e:
                console.print(f"[red]❌ class_name failed: {e}[/red]")

        # STRATEGY 6: Coordinate-based fallback (last resort)
        if bounds:
            try:
                console.print(f"[yellow]🎯 Attempting coordinate-based fallback...[/yellow]")
                
                bounds_dict = None
                if isinstance(bounds, str):
                    # Parse string format like "[0,0][100,100]"
                    import re
                    matches = re.findall(r'\[(\d+),(\d+)\]', bounds)
                    if len(matches) >= 2:
                        left, top = int(matches[0][0]), int(matches[0][1])
                        right, bottom = int(matches[1][0]), int(matches[1][1])
                        bounds_dict = {"left": left, "top": top, "right": right, "bottom": bottom}
                elif isinstance(bounds, dict):
                    bounds_dict = bounds
                
                if bounds_dict and all(k in bounds_dict for k in ['left', 'top', 'right', 'bottom']):
                    center_x = (bounds_dict['left'] + bounds_dict['right']) // 2
                    center_y = (bounds_dict['top'] + bounds_dict['bottom']) // 2
                    
                    # Validate coordinates
                    screen_info = self.device.info
                    screen_width = screen_info.get('displayWidth', 1080)
                    screen_height = screen_info.get('displayHeight', 1920)
                    
                    if (0 <= center_x <= screen_width and 0 <= center_y <= screen_height):
                        console.print(f"[green]✅ Using coordinate fallback: ({center_x}, {center_y})[/green]")
                        
                        # Create coordinate-based element wrapper
                        class CoordinateElement:
                            def __init__(self, x, y, device):
                                self.x, self.y, self.device = x, y, device
                                
                            def click(self):
                                self.device.click(self.x, self.y)
                                
                            def exists(self):
                                return True
                                
                            def info(self):
                                return {"clickable": True, "enabled": True}
                        
                        return CoordinateElement(center_x, center_y, self.device)
                    else:
                        console.print(f"[red]❌ Coordinates out of bounds: ({center_x}, {center_y})[/red]")
                
            except Exception as coord_e:
                console.print(f"[red]❌ Coordinate fallback failed: {coord_e}[/red]")

        # STRATEGY 7: Screen refresh and retry (if element might be stale)
        console.print(f"[yellow]🔄 Attempting screen refresh and retry...[/yellow]")
        try:
            # Wait a moment for UI to settle
            await asyncio.sleep(0.5)
            
            # Try the most promising strategy again
            if resource_id:
                element = self.device(resourceId=resource_id)
                if element.exists():
                    console.print(f"[green]✅ Found after refresh using resource_id[/green]")
                    return element
            
            if text:
                element = self.device(text=text)
                if element.exists():
                    console.print(f"[green]✅ Found after refresh using text[/green]")
                    return element
                    
        except Exception as refresh_e:
            console.print(f"[red]❌ Refresh retry failed: {refresh_e}[/red]")

        console.print(f"[red]❌ ALL ENHANCED STRATEGIES FAILED - element cannot be recreated[/red]")
        return None

    except Exception as e:
        console.print(f"[red]❌ Enhanced recreation failed with error: {e}[/red]")
        return None


# ADDITIONAL IMPROVEMENTS

# ELEMENT_STALENESS_DETECTION

async def _detect_element_staleness(self, element_data: Dict) -> bool:
    """Detect if element data might be stale"""
    try:
        # Check if element was collected more than 30 seconds ago
        timestamp = element_data.get("timestamp")
        if timestamp:
            from datetime import datetime
            element_time = datetime.fromisoformat(timestamp)
            age = (datetime.now() - element_time).total_seconds()
            if age > 30:
                console.print(f"[yellow]⚠️ Element data is {age:.1f}s old - might be stale[/yellow]")
                return True
        return False
    except:
        return False


# RETRY_MECHANISM

async def _retry_element_recreation(self, element_data: Dict, max_retries: int = 3) -> Optional[UiObject]:
    """Retry element recreation with delays"""
    for attempt in range(max_retries):
        console.print(f"[cyan]🔄 Recreation attempt {attempt + 1}/{max_retries}[/cyan]")
        
        element = await self._recreate_element_from_data_enhanced(element_data)
        if element:
            return element
            
        if attempt < max_retries - 1:
            delay = 0.5 * (attempt + 1)  # Exponential backoff
            console.print(f"[yellow]⏳ Waiting {delay}s before retry...[/yellow]")
            await asyncio.sleep(delay)
    
    return None


# SCREEN_STATE_VALIDATION

async def _validate_screen_state_for_recreation(self) -> bool:
    """Validate that screen state is suitable for element recreation"""
    try:
        # Check if app is responsive
        current_app = self.device.app_current()
        if not current_app:
            console.print("[red]❌ No app currently active[/red]")
            return False
            
        # Check if screen has elements
        try:
            elements = self.device.xpath("//node").all()
            if len(elements) == 0:
                console.print("[red]❌ No elements found on screen[/red]")
                return False
        except:
            console.print("[red]❌ Cannot access screen elements[/red]")
            return False
            
        console.print("[green]✅ Screen state is valid for recreation[/green]")
        return True
        
    except Exception as e:
        console.print(f"[red]❌ Screen state validation failed: {e}[/red]")
        return False


