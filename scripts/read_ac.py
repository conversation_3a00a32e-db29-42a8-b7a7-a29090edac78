#!/usr/bin/env python3

import pandas as pd

# Read the Excel file
import os
script_dir = os.path.dirname(os.path.abspath(__file__))
excel_path = os.path.join(script_dir, "..", "testcases", "AC.xlsx")
df = pd.read_excel(excel_path)

# Print the column names
print("Columns:")
print(df.columns.tolist())

# Print all rows
print("\nAll rows:")
pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
pd.set_option('display.width', 1000)
pd.set_option('display.max_colwidth', 1000)
print(df.to_string())