#!/usr/bin/env python3
"""
Fix Feature Clicking Issue
This script diagnoses and fixes the issue where AI analysis fails because it cannot click feature elements.

Problem Analysis:
- AI successfully collects 481 elements and achieves 100% structure coverage
- But fails to click any of the 10 features (<PERSON><PERSON>K, <PERSON><PERSON> Murid, etc.)
- All features fail with "Failed to recreate element" error
- Results in 0% feature coverage and analysis failure

Root Cause: Element locator/recreation issue preventing feature interaction
"""

import asyncio
import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from utils.android_manager import AndroidManager
from utils.console import get_centralized_console
from storage.persistent_excel_manager import PersistentExcelManager

console = get_centralized_console()

async def diagnose_feature_clicking_issue():
    """Diagnose why feature elements cannot be clicked"""
    
    console.print("[bold blue]🔧 Diagnosing Feature Clicking Issue[/bold blue]")
    console.print("=" * 60)
    
    try:
        # Initialize Android manager
        console.print("[cyan]📱 Connecting to Android device...[/cyan]")
        android_manager = AndroidManager()
        device = await android_manager.get_device()
        
        if not device:
            console.print("[red]❌ No Android device connected[/red]")
            return False
            
        console.print(f"[green]✅ Connected to device: {device.serial}[/green]")
        
        # Check current app
        current_app = device.app_current()
        console.print(f"[cyan]📱 Current app: {current_app.get('package', 'Unknown')}[/cyan]")
        
        # Load persistent Excel data to see what elements were found
        console.print("[cyan]📂 Loading persistent Excel data...[/cyan]")
        excel_manager = PersistentExcelManager("com.kemendikdasmen.rumahpendidikan")
        
        # Check if persistent file exists
        persistent_file = "rumahpendidikan_persistent_analysis.xlsx"
        if os.path.exists(persistent_file):
            console.print(f"[green]✅ Found persistent file: {persistent_file}[/green]")
            
            # Try to read elements
            try:
                elements_data = excel_manager.load_elements()
                console.print(f"[green]✅ Loaded {len(elements_data)} elements from persistent file[/green]")
                
                # Look for feature elements
                feature_names = [
                    "Ruang GTK", "Ruang Murid", "Ruang Sekolah", "Ruang Bahasa",
                    "Ruang Pemerintah", "Ruang Mitra", "Ruang Publik", "Ruang Orang Tua",
                    "Sumber Belajar", "Pusat Perbukuan"
                ]
                
                console.print("[yellow]🔍 Searching for feature elements in collected data...[/yellow]")
                found_features = []
                
                for element in elements_data:
                    element_text = element.get('text', '').strip()
                    element_desc = element.get('content_desc', '').strip()
                    element_id = element.get('resource_id', '').strip()
                    
                    for feature_name in feature_names:
                        if (feature_name in element_text or 
                            feature_name in element_desc or 
                            feature_name.lower().replace(' ', '') in element_id.lower()):
                            
                            found_features.append({
                                'feature': feature_name,
                                'element': element,
                                'match_type': 'text' if feature_name in element_text else 
                                            'desc' if feature_name in element_desc else 'id'
                            })
                
                console.print(f"[cyan]📊 Found {len(found_features)} potential feature elements:[/cyan]")
                
                for i, feature_match in enumerate(found_features[:10]):  # Show first 10
                    feature = feature_match['feature']
                    element = feature_match['element']
                    match_type = feature_match['match_type']
                    
                    console.print(f"[green]  {i+1}. {feature} (matched by {match_type}):[/green]")
                    console.print(f"[cyan]     Text: '{element.get('text', '')}'[/cyan]")
                    console.print(f"[cyan]     Desc: '{element.get('content_desc', '')}'[/cyan]")
                    console.print(f"[cyan]     ID: '{element.get('resource_id', '')}'[/cyan]")
                    console.print(f"[cyan]     Class: '{element.get('class_name', '')}'[/cyan]")
                    console.print(f"[cyan]     Clickable: {element.get('clickable', False)}[/cyan]")
                    console.print(f"[cyan]     Bounds: {element.get('bounds', '')}[/cyan]")
                    console.print()
                
                # Test clicking on found elements
                if found_features:
                    console.print("[yellow]🧪 Testing element clicking...[/yellow]")
                    
                    for i, feature_match in enumerate(found_features[:3]):  # Test first 3
                        feature = feature_match['feature']
                        element = feature_match['element']
                        
                        console.print(f"[cyan]Testing click on {feature}...[/cyan]")
                        
                        # Try different locator strategies
                        strategies = []
                        
                        # Strategy 1: By text
                        if element.get('text'):
                            strategies.append(('text', element['text']))
                        
                        # Strategy 2: By content description
                        if element.get('content_desc'):
                            strategies.append(('desc', element['content_desc']))
                        
                        # Strategy 3: By resource ID
                        if element.get('resource_id'):
                            strategies.append(('id', element['resource_id']))
                        
                        # Strategy 4: By XPath
                        if element.get('xpath'):
                            strategies.append(('xpath', element['xpath']))
                        
                        success = False
                        for strategy_name, locator in strategies:
                            try:
                                console.print(f"[cyan]  Trying {strategy_name}: '{locator}'[/cyan]")
                                
                                if strategy_name == 'text':
                                    found_element = device(text=locator)
                                elif strategy_name == 'desc':
                                    found_element = device(description=locator)
                                elif strategy_name == 'id':
                                    found_element = device(resourceId=locator)
                                elif strategy_name == 'xpath':
                                    found_element = device.xpath(locator)
                                else:
                                    continue
                                
                                if found_element.exists:
                                    console.print(f"[green]    ✅ Element found with {strategy_name}[/green]")
                                    
                                    # Try to get element info
                                    info = found_element.info
                                    console.print(f"[cyan]    Clickable: {info.get('clickable', False)}[/cyan]")
                                    console.print(f"[cyan]    Enabled: {info.get('enabled', False)}[/cyan]")
                                    console.print(f"[cyan]    Bounds: {info.get('bounds', {})}[/cyan]")
                                    
                                    success = True
                                    break
                                else:
                                    console.print(f"[yellow]    ⚠️ Element not found with {strategy_name}[/yellow]")
                                    
                            except Exception as e:
                                console.print(f"[red]    ❌ Error with {strategy_name}: {e}[/red]")
                        
                        if not success:
                            console.print(f"[red]  ❌ Could not locate {feature} with any strategy[/red]")
                        
                        console.print()
                
            except Exception as e:
                console.print(f"[red]❌ Error reading persistent file: {e}[/red]")
        else:
            console.print(f"[yellow]⚠️ Persistent file not found: {persistent_file}[/yellow]")
        
        # Test current screen elements
        console.print("[yellow]🔍 Testing current screen elements...[/yellow]")
        try:
            # Get current screen dump
            current_elements = device.dump_hierarchy()
            console.print("[green]✅ Successfully got current screen hierarchy[/green]")
            
            # Look for feature elements on current screen
            feature_names = ["Ruang GTK", "Ruang Murid", "Ruang Sekolah", "Ruang Bahasa"]
            
            for feature_name in feature_names:
                console.print(f"[cyan]Looking for '{feature_name}' on current screen...[/cyan]")
                
                # Try multiple locator strategies
                strategies = [
                    ('text', lambda: device(text=feature_name)),
                    ('textContains', lambda: device(textContains=feature_name)),
                    ('description', lambda: device(description=feature_name)),
                    ('descriptionContains', lambda: device(descriptionContains=feature_name)),
                ]
                
                found = False
                for strategy_name, locator_func in strategies:
                    try:
                        element = locator_func()
                        if element.exists:
                            console.print(f"[green]  ✅ Found with {strategy_name}[/green]")
                            info = element.info
                            console.print(f"[cyan]    Clickable: {info.get('clickable', False)}[/cyan]")
                            console.print(f"[cyan]    Enabled: {info.get('enabled', False)}[/cyan]")
                            found = True
                            break
                    except Exception as e:
                        console.print(f"[yellow]    ⚠️ {strategy_name} failed: {e}[/yellow]")
                
                if not found:
                    console.print(f"[red]  ❌ '{feature_name}' not found on current screen[/red]")
        
        except Exception as e:
            console.print(f"[red]❌ Error testing current screen: {e}[/red]")
        
        console.print("\n[bold green]🎯 Diagnosis Summary:[/bold green]")
        console.print("[cyan]The issue appears to be that the AI can collect elements but cannot")
        console.print("recreate/locate them for clicking. This suggests:[/cyan]")
        console.print("[yellow]1. Element locators may be stale or incorrect[/yellow]")
        console.print("[yellow]2. Elements may not be clickable when found[/yellow]")
        console.print("[yellow]3. Screen state may change between collection and clicking[/yellow]")
        console.print("[yellow]4. Locator strategies may need improvement[/yellow]")
        
        return True
        
    except Exception as e:
        console.print(f"[red]❌ Diagnosis failed: {e}[/red]")
        return False

async def suggest_fixes():
    """Suggest fixes for the feature clicking issue"""
    
    console.print("\n[bold blue]💡 Suggested Fixes:[/bold blue]")
    console.print("=" * 60)
    
    fixes = [
        {
            "title": "1. Improve Element Locator Strategies",
            "description": "Enhance the element recreation logic to use multiple fallback strategies",
            "actions": [
                "Use multiple locator types (text, description, resource-id, xpath)",
                "Add fuzzy text matching for partial matches",
                "Implement coordinate-based clicking as fallback",
                "Add element staleness detection and refresh"
            ]
        },
        {
            "title": "2. Add Real-time Element Validation",
            "description": "Validate elements exist and are clickable before attempting to click",
            "actions": [
                "Check element.exists before clicking",
                "Verify element.clickable property",
                "Ensure element is visible and enabled",
                "Add retry logic with short delays"
            ]
        },
        {
            "title": "3. Implement Screen State Management",
            "description": "Ensure consistent screen state between element collection and clicking",
            "actions": [
                "Take screenshots before and after element collection",
                "Verify app is still on the same screen before clicking",
                "Add navigation state tracking",
                "Implement screen refresh if state changes"
            ]
        },
        {
            "title": "4. Enhanced Error Handling",
            "description": "Better error reporting and recovery for failed clicks",
            "actions": [
                "Log detailed error information for failed clicks",
                "Implement alternative clicking methods",
                "Add element bounds-based clicking",
                "Provide fallback to coordinate clicking"
            ]
        }
    ]
    
    for fix in fixes:
        console.print(f"[bold yellow]{fix['title']}[/bold yellow]")
        console.print(f"[cyan]{fix['description']}[/cyan]")
        for action in fix['actions']:
            console.print(f"[green]  • {action}[/green]")
        console.print()

if __name__ == "__main__":
    async def main():
        success = await diagnose_feature_clicking_issue()
        await suggest_fixes()
        
        if success:
            console.print("[bold green]🎉 Diagnosis completed successfully![/bold green]")
            console.print("[cyan]Use the suggested fixes to resolve the feature clicking issue.[/cyan]")
        else:
            console.print("[bold red]❌ Diagnosis failed. Check device connection and try again.[/bold red]")
    
    asyncio.run(main())
