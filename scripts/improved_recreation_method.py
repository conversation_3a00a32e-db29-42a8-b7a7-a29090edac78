
async def _recreate_element_from_data_improved(self, element_data: Dict) -> Optional[UiObject]:
    """Improved element recreation with optimized strategies based on testing"""
    try:
        if not element_data or not self.device:
            return None

        # Extract element attributes
        text = element_data.get("text", "").strip()
        content_desc = element_data.get("content_desc", "").strip()
        resource_id = element_data.get("resource_id", "").strip()
        class_name = element_data.get("class_name", "").strip()
        bounds = element_data.get("bounds", {})

        console.print(f"[cyan]🔍 Recreating: text='{text[:20]}...', desc='{content_desc[:20]}...', id='{resource_id}'[/cyan]")

        # Strategy priority based on test results (most successful first)
        strategies = []
        
        # High-priority strategies (usually most reliable)
        if resource_id:
            strategies.append(("resource_id", lambda: self.device(resourceId=resource_id)))
        
        if text:
            strategies.append(("text_exact", lambda: self.device(text=text)))
            strategies.append(("text_contains", lambda: self.device(textContains=text)))
        
        if content_desc:
            strategies.append(("desc_exact", lambda: self.device(description=content_desc)))
            strategies.append(("desc_contains", lambda: self.device(descriptionContains=content_desc)))
        
        # Combined strategies
        if text and class_name:
            strategies.append(("text_class", lambda: self.device(text=text, className=class_name)))
        
        if text and resource_id:
            strategies.append(("text_id", lambda: self.device(text=text, resourceId=resource_id)))
        
        # XPath strategies
        if text:
            strategies.append(("xpath_text", lambda: self.device.xpath(f"//*[@text='{text}']")))
        
        # Class-based (usually less reliable)
        if class_name:
            strategies.append(("class_name", lambda: self.device(className=class_name)))

        # Try each strategy with timeout and error handling
        for strategy_name, strategy_func in strategies:
            try:
                console.print(f"[cyan]  Trying {strategy_name}...[/cyan]")
                element = strategy_func()
                
                if hasattr(element, 'exists'):
                    if element.exists():
                        console.print(f"[green]✅ Found using {strategy_name}[/green]")
                        return element
                elif hasattr(element, 'get'):  # XPath element
                    try:
                        xpath_element = element.get(timeout=2)
                        if xpath_element:
                            console.print(f"[green]✅ Found using {strategy_name}[/green]")
                            return xpath_element
                    except:
                        pass
                        
                console.print(f"[yellow]⚠️ {strategy_name} - not found[/yellow]")
                
            except Exception as e:
                console.print(f"[red]❌ {strategy_name} failed: {e}[/red]")
                continue

        # Coordinate fallback as last resort
        if isinstance(bounds, dict) and all(k in bounds for k in ['left', 'top', 'right', 'bottom']):
            console.print("[yellow]🎯 Attempting coordinate-based fallback[/yellow]")
            center_x = (bounds['left'] + bounds['right']) // 2
            center_y = (bounds['top'] + bounds['bottom']) // 2
            
            # Create a mock element for coordinate clicking
            class CoordinateElement:
                def __init__(self, x, y, device):
                    self.x, self.y, self.device = x, y, device
                def click(self):
                    self.device.click(self.x, self.y)
                def exists(self):
                    return True
            
            console.print(f"[green]✅ Using coordinate fallback ({center_x}, {center_y})[/green]")
            return CoordinateElement(center_x, center_y, self.device)

        console.print("[red]❌ All recreation strategies failed[/red]")
        return None
        
    except Exception as e:
        console.print(f"[red]❌ Recreation failed with error: {e}[/red]")
        return None
