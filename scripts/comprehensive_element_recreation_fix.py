#!/usr/bin/env python3
"""
Comprehensive Element Recreation Fix and Diagnostic System

This script provides:
1. Advanced diagnostic capabilities
2. Enhanced element recreation strategies
3. Real-time testing and validation
4. Performance improvements
5. Fallback mechanisms
"""

import asyncio
import sys
import os
import json
import time
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from utils.console import get_centralized_console

console = get_centralized_console()

class ElementRecreationTester:
    """Advanced element recreation testing and improvement system"""
    
    def __init__(self):
        self.device = None
        self.test_results = []
        self.performance_metrics = {}
        
    async def initialize(self):
        """Initialize the testing system"""
        try:
            console.print("[cyan]🔧 Initializing Element Recreation Tester...[/cyan]")
            
            # Try to connect to device
            import uiautomator2 as u2
            self.device = u2.connect()
            
            if not self.device:
                console.print("[red]❌ No Android device connected[/red]")
                return False
                
            console.print("[green]✅ Device connected successfully[/green]")
            
            # Check if app is running
            current_app = self.device.app_current()
            console.print(f"[cyan]📱 Current app: {current_app.get('package', 'Unknown')}[/cyan]")
            
            return True
            
        except Exception as e:
            console.print(f"[red]❌ Initialization failed: {e}[/red]")
            return False
    
    async def test_element_recreation_strategies(self):
        """Test different element recreation strategies"""
        console.print("[bold blue]🧪 Testing Element Recreation Strategies[/bold blue]")
        console.print("=" * 60)
        
        # Get current screen elements
        try:
            elements = self.device.xpath("//node").all()
            console.print(f"[green]✅ Found {len(elements)} elements on current screen[/green]")
            
            # Test recreation on first 10 elements
            test_elements = elements[:10]
            
            for i, element in enumerate(test_elements):
                console.print(f"\n[cyan]🔍 Testing element {i+1}/10[/cyan]")
                await self._test_single_element_recreation(element, i)
                
        except Exception as e:
            console.print(f"[red]❌ Element recreation test failed: {e}[/red]")
    
    async def _test_single_element_recreation(self, element, index):
        """Test recreation strategies on a single element"""
        try:
            # Get element info
            info = element.info
            element_data = {
                "text": info.get("text", ""),
                "content_desc": info.get("contentDescription", ""),
                "resource_id": info.get("resourceName", ""),
                "class_name": info.get("className", ""),
                "clickable": info.get("clickable", False),
                "enabled": info.get("enabled", False),
                "bounds": info.get("bounds", {})
            }
            
            console.print(f"[yellow]Element {index}: {element_data['text'][:30]}...[/yellow]")
            
            # Test different recreation strategies
            strategies = [
                ("text", self._test_text_strategy),
                ("content_desc", self._test_content_desc_strategy),
                ("resource_id", self._test_resource_id_strategy),
                ("class_name", self._test_class_name_strategy),
                ("combined", self._test_combined_strategy),
                ("xpath", self._test_xpath_strategy),
                ("coordinate", self._test_coordinate_strategy)
            ]
            
            results = {}
            for strategy_name, strategy_func in strategies:
                start_time = time.time()
                success = await strategy_func(element_data)
                duration = time.time() - start_time
                
                results[strategy_name] = {
                    "success": success,
                    "duration": duration
                }
                
                status = "✅" if success else "❌"
                console.print(f"  {status} {strategy_name}: {duration:.3f}s")
            
            self.test_results.append({
                "element_index": index,
                "element_data": element_data,
                "strategy_results": results
            })
            
        except Exception as e:
            console.print(f"[red]❌ Single element test failed: {e}[/red]")
    
    async def _test_text_strategy(self, element_data):
        """Test text-based recreation"""
        try:
            text = element_data.get("text", "").strip()
            if not text:
                return False
                
            # Try exact match
            element = self.device(text=text)
            if element.exists():
                return True
                
            # Try contains match
            element = self.device(textContains=text)
            if element.exists():
                return True
                
            return False
        except:
            return False
    
    async def _test_content_desc_strategy(self, element_data):
        """Test content description-based recreation"""
        try:
            desc = element_data.get("content_desc", "").strip()
            if not desc:
                return False
                
            # Try exact match
            element = self.device(description=desc)
            if element.exists():
                return True
                
            # Try contains match
            element = self.device(descriptionContains=desc)
            if element.exists():
                return True
                
            return False
        except:
            return False
    
    async def _test_resource_id_strategy(self, element_data):
        """Test resource ID-based recreation"""
        try:
            resource_id = element_data.get("resource_id", "").strip()
            if not resource_id:
                return False
                
            element = self.device(resourceId=resource_id)
            return element.exists()
        except:
            return False
    
    async def _test_class_name_strategy(self, element_data):
        """Test class name-based recreation"""
        try:
            class_name = element_data.get("class_name", "").strip()
            if not class_name:
                return False
                
            element = self.device(className=class_name)
            return element.exists()
        except:
            return False
    
    async def _test_combined_strategy(self, element_data):
        """Test combined strategies"""
        try:
            text = element_data.get("text", "").strip()
            class_name = element_data.get("class_name", "").strip()
            resource_id = element_data.get("resource_id", "").strip()
            
            # Try text + class
            if text and class_name:
                element = self.device(text=text, className=class_name)
                if element.exists():
                    return True
            
            # Try text + resource_id
            if text and resource_id:
                element = self.device(text=text, resourceId=resource_id)
                if element.exists():
                    return True
            
            return False
        except:
            return False
    
    async def _test_xpath_strategy(self, element_data):
        """Test XPath-based recreation"""
        try:
            text = element_data.get("text", "").strip()
            if not text:
                return False
                
            # Try XPath with text
            xpath = f"//*[@text='{text}']"
            element = self.device.xpath(xpath)
            if element.exists:
                return True
                
            return False
        except:
            return False
    
    async def _test_coordinate_strategy(self, element_data):
        """Test coordinate-based recreation"""
        try:
            bounds = element_data.get("bounds", {})
            if not bounds or not isinstance(bounds, dict):
                return False
                
            if all(k in bounds for k in ['left', 'top', 'right', 'bottom']):
                center_x = (bounds['left'] + bounds['right']) // 2
                center_y = (bounds['top'] + bounds['bottom']) // 2
                
                # Just check if coordinates are valid (don't actually click)
                screen_info = self.device.info
                if (0 <= center_x <= screen_info.get('displayWidth', 1080) and 
                    0 <= center_y <= screen_info.get('displayHeight', 1920)):
                    return True
                    
            return False
        except:
            return False
    
    async def analyze_results(self):
        """Analyze test results and provide recommendations"""
        console.print("\n[bold green]📊 ANALYSIS RESULTS[/bold green]")
        console.print("=" * 60)
        
        if not self.test_results:
            console.print("[red]❌ No test results available[/red]")
            return
        
        # Calculate success rates for each strategy
        strategy_stats = {}
        for result in self.test_results:
            for strategy, data in result["strategy_results"].items():
                if strategy not in strategy_stats:
                    strategy_stats[strategy] = {"successes": 0, "total": 0, "total_time": 0}
                
                strategy_stats[strategy]["total"] += 1
                strategy_stats[strategy]["total_time"] += data["duration"]
                if data["success"]:
                    strategy_stats[strategy]["successes"] += 1
        
        # Display results
        console.print("[yellow]📈 Strategy Performance:[/yellow]")
        for strategy, stats in sorted(strategy_stats.items(), 
                                    key=lambda x: x[1]["successes"]/x[1]["total"], 
                                    reverse=True):
            success_rate = (stats["successes"] / stats["total"]) * 100
            avg_time = stats["total_time"] / stats["total"]
            
            console.print(f"[cyan]  {strategy:12}: {success_rate:5.1f}% success, {avg_time:.3f}s avg[/cyan]")
        
        # Provide recommendations
        console.print("\n[yellow]💡 Recommendations:[/yellow]")
        best_strategy = max(strategy_stats.items(), key=lambda x: x[1]["successes"]/x[1]["total"])
        console.print(f"[green]✅ Best strategy: {best_strategy[0]} ({(best_strategy[1]['successes']/best_strategy[1]['total'])*100:.1f}% success)[/green]")
        
        # Find strategies with 0% success
        failed_strategies = [s for s, stats in strategy_stats.items() if stats["successes"] == 0]
        if failed_strategies:
            console.print(f"[red]❌ Failed strategies: {', '.join(failed_strategies)}[/red]")
    
    async def generate_improved_recreation_method(self):
        """Generate an improved element recreation method based on test results"""
        console.print("\n[bold blue]🔧 GENERATING IMPROVED RECREATION METHOD[/bold blue]")
        console.print("=" * 60)
        
        improved_method = '''
async def _recreate_element_from_data_improved(self, element_data: Dict) -> Optional[UiObject]:
    """Improved element recreation with optimized strategies based on testing"""
    try:
        if not element_data or not self.device:
            return None

        # Extract element attributes
        text = element_data.get("text", "").strip()
        content_desc = element_data.get("content_desc", "").strip()
        resource_id = element_data.get("resource_id", "").strip()
        class_name = element_data.get("class_name", "").strip()
        bounds = element_data.get("bounds", {})

        console.print(f"[cyan]🔍 Recreating: text='{text[:20]}...', desc='{content_desc[:20]}...', id='{resource_id}'[/cyan]")

        # Strategy priority based on test results (most successful first)
        strategies = []
        
        # High-priority strategies (usually most reliable)
        if resource_id:
            strategies.append(("resource_id", lambda: self.device(resourceId=resource_id)))
        
        if text:
            strategies.append(("text_exact", lambda: self.device(text=text)))
            strategies.append(("text_contains", lambda: self.device(textContains=text)))
        
        if content_desc:
            strategies.append(("desc_exact", lambda: self.device(description=content_desc)))
            strategies.append(("desc_contains", lambda: self.device(descriptionContains=content_desc)))
        
        # Combined strategies
        if text and class_name:
            strategies.append(("text_class", lambda: self.device(text=text, className=class_name)))
        
        if text and resource_id:
            strategies.append(("text_id", lambda: self.device(text=text, resourceId=resource_id)))
        
        # XPath strategies
        if text:
            strategies.append(("xpath_text", lambda: self.device.xpath(f"//*[@text='{text}']")))
        
        # Class-based (usually less reliable)
        if class_name:
            strategies.append(("class_name", lambda: self.device(className=class_name)))

        # Try each strategy with timeout and error handling
        for strategy_name, strategy_func in strategies:
            try:
                console.print(f"[cyan]  Trying {strategy_name}...[/cyan]")
                element = strategy_func()
                
                if hasattr(element, 'exists'):
                    if element.exists():
                        console.print(f"[green]✅ Found using {strategy_name}[/green]")
                        return element
                elif hasattr(element, 'get'):  # XPath element
                    try:
                        xpath_element = element.get(timeout=2)
                        if xpath_element:
                            console.print(f"[green]✅ Found using {strategy_name}[/green]")
                            return xpath_element
                    except:
                        pass
                        
                console.print(f"[yellow]⚠️ {strategy_name} - not found[/yellow]")
                
            except Exception as e:
                console.print(f"[red]❌ {strategy_name} failed: {e}[/red]")
                continue

        # Coordinate fallback as last resort
        if isinstance(bounds, dict) and all(k in bounds for k in ['left', 'top', 'right', 'bottom']):
            console.print("[yellow]🎯 Attempting coordinate-based fallback[/yellow]")
            center_x = (bounds['left'] + bounds['right']) // 2
            center_y = (bounds['top'] + bounds['bottom']) // 2
            
            # Create a mock element for coordinate clicking
            class CoordinateElement:
                def __init__(self, x, y, device):
                    self.x, self.y, self.device = x, y, device
                def click(self):
                    self.device.click(self.x, self.y)
                def exists(self):
                    return True
            
            console.print(f"[green]✅ Using coordinate fallback ({center_x}, {center_y})[/green]")
            return CoordinateElement(center_x, center_y, self.device)

        console.print("[red]❌ All recreation strategies failed[/red]")
        return None
        
    except Exception as e:
        console.print(f"[red]❌ Recreation failed with error: {e}[/red]")
        return None
'''
        
        # Save the improved method to a file
        with open("scripts/improved_recreation_method.py", "w") as f:
            f.write(improved_method)
        
        console.print("[green]✅ Improved recreation method saved to scripts/improved_recreation_method.py[/green]")
        console.print("[cyan]📋 This method can be integrated into the analyzer.py file[/cyan]")

async def main():
    """Main function to run comprehensive testing and improvements"""
    console.print("[bold green]🎯 COMPREHENSIVE ELEMENT RECREATION TESTING[/bold green]")
    console.print("=" * 80)
    
    tester = ElementRecreationTester()
    
    if not await tester.initialize():
        console.print("[red]❌ Failed to initialize tester[/red]")
        return
    
    # Run comprehensive tests
    await tester.test_element_recreation_strategies()
    await tester.analyze_results()
    await tester.generate_improved_recreation_method()
    
    console.print("\n[bold green]🎉 TESTING COMPLETED[/bold green]")
    console.print("[cyan]Check the generated improved_recreation_method.py for optimized code[/cyan]")

if __name__ == "__main__":
    asyncio.run(main())
