#!/usr/bin/env python3
"""
Disable Mobile Highlighting Script
This script disables the problematic visual highlighting in mobile-test
to prevent interference with test execution.
"""

import sys
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from mobile.tester import MobileTester
from utils.console import get_centralized_console

console = get_centralized_console()

def disable_mobile_highlighting():
    """Disable mobile highlighting to prevent test interference"""
    
    console.print("[bold yellow]🔧 Disabling Mobile Visual Highlighting[/bold yellow]")
    console.print("=" * 60)
    
    try:
        # Create a mobile tester instance
        tester = MobileTester()
        
        # Disable all visual highlighting
        tester.disable_visual_highlighting()
        
        console.print("[green]✅ Mobile visual highlighting has been disabled[/green]")
        console.print("[cyan]📝 Tests will now use console feedback only[/cyan]")
        console.print("[cyan]🚀 This should resolve any highlighting-related issues[/cyan]")
        
        return True
        
    except Exception as e:
        console.print(f"[red]❌ Error disabling highlighting: {e}[/red]")
        return False

def enable_simple_highlighting():
    """Enable only simple, safe highlighting"""
    
    console.print("[bold blue]🔧 Enabling Simple Mobile Highlighting[/bold blue]")
    console.print("=" * 60)
    
    try:
        # Create a mobile tester instance
        tester = MobileTester()
        
        # Enable simple highlighting only
        tester.enable_simple_highlighting()
        
        console.print("[green]✅ Simple mobile highlighting has been enabled[/green]")
        console.print("[cyan]📝 Only safe, non-interfering highlighting will be used[/cyan]")
        
        return True
        
    except Exception as e:
        console.print(f"[red]❌ Error enabling simple highlighting: {e}[/red]")
        return False

def main():
    """Main function"""
    console.print("[bold cyan]🎯 Mobile Highlighting Configuration Tool[/bold cyan]\n")
    
    console.print("Choose an option:")
    console.print("1. Disable all visual highlighting (recommended if having issues)")
    console.print("2. Enable simple, safe highlighting only")
    console.print("3. Exit")
    
    try:
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == "1":
            success = disable_mobile_highlighting()
        elif choice == "2":
            success = enable_simple_highlighting()
        elif choice == "3":
            console.print("[yellow]Exiting...[/yellow]")
            return
        else:
            console.print("[red]Invalid choice. Please enter 1, 2, or 3.[/red]")
            return
            
        if success:
            console.print("\n[bold green]✅ Configuration updated successfully![/bold green]")
            console.print("[cyan]You can now run mobile-test without highlighting issues.[/cyan]")
        else:
            console.print("\n[bold red]❌ Configuration failed.[/bold red]")
            
    except KeyboardInterrupt:
        console.print("\n[yellow]Operation cancelled by user.[/yellow]")
    except Exception as e:
        console.print(f"\n[red]Error: {e}[/red]")

if __name__ == "__main__":
    main()
